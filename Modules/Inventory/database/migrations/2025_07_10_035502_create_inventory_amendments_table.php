<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('inventory_amendments', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('product_id');
            $table->integer('recorded_quantity'); // Original recorded quantity
            $table->integer('actual_quantity'); // Actual counted quantity
            $table->integer('variance'); // Difference (actual - recorded)
            $table->enum('status', ['pending', 'approved', 'rejected'])->default('pending');
            $table->text('notes')->nullable(); // Amendment notes/reasons
            $table->unsignedBigInteger('amended_by'); // User who performed the amendment
            $table->unsignedBigInteger('approved_by')->nullable(); // User who approved the amendment
            $table->timestamp('amendment_date');
            $table->timestamp('approved_date')->nullable();
            $table->timestamps();

            $table->foreign('product_id')->references('id')->on('inventory_products')->onDelete('cascade');
            $table->foreign('amended_by')->references('id')->on('users')->onDelete('cascade');
            $table->foreign('approved_by')->references('id')->on('users')->onDelete('set null');
            $table->index(['product_id', 'amendment_date']);
            $table->index(['status', 'amendment_date']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('inventory_amendments');
    }
};
