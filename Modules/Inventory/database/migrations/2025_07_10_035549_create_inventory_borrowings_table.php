<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('inventory_borrowings', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('product_id');
            $table->integer('quantity_borrowed');
            $table->string('borrower_name'); // Name of the person borrowing
            $table->string('borrower_email')->nullable();
            $table->string('borrower_phone')->nullable();
            $table->string('department')->nullable();
            $table->text('purpose')->nullable(); // Purpose of borrowing
            $table->enum('status', ['borrowed', 'returned', 'overdue', 'lost'])->default('borrowed');
            $table->timestamp('borrowed_date');
            $table->timestamp('expected_return_date')->nullable();
            $table->timestamp('actual_return_date')->nullable();
            $table->integer('quantity_returned')->default(0);
            $table->text('return_notes')->nullable();
            $table->unsignedBigInteger('issued_by'); // User who issued the item
            $table->unsignedBigInteger('returned_to')->nullable(); // User who received the return
            $table->timestamps();

            $table->foreign('product_id')->references('id')->on('inventory_products')->onDelete('cascade');
            $table->foreign('issued_by')->references('id')->on('users')->onDelete('cascade');
            $table->foreign('returned_to')->references('id')->on('users')->onDelete('set null');
            $table->index(['product_id', 'status']);
            $table->index(['status', 'borrowed_date']);
            $table->index(['expected_return_date', 'status']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('inventory_borrowings');
    }
};
