<?php

namespace Modules\Inventory\Database\Seeders;

use Illuminate\Database\Seeder;
use Modules\Inventory\Models\Category;
use Modules\Inventory\Models\Supplier;
use Modules\Inventory\Models\Product;

class ComputerSystemsServicingSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create main categories
        $networkingCategory = Category::create([
            'name' => 'Networking Equipment',
            'description' => 'Network infrastructure and connectivity equipment',
            'slug' => 'networking-equipment',
            'is_active' => true,
            'sort_order' => 1,
        ]);

        $computersCategory = Category::create([
            'name' => 'Computer Systems',
            'description' => 'Desktop computers, laptops, and servers',
            'slug' => 'computer-systems',
            'is_active' => true,
            'sort_order' => 2,
        ]);

        $toolsCategory = Category::create([
            'name' => 'Tools & Instruments',
            'description' => 'Hand tools, testing equipment, and maintenance tools',
            'slug' => 'tools-instruments',
            'is_active' => true,
            'sort_order' => 3,
        ]);

        $materialsCategory = Category::create([
            'name' => 'Materials & Components',
            'description' => 'Cables, connectors, and electronic components',
            'slug' => 'materials-components',
            'is_active' => true,
            'sort_order' => 4,
        ]);

        $softwareCategory = Category::create([
            'name' => 'Software & Licenses',
            'description' => 'Operating systems, applications, and software licenses',
            'slug' => 'software-licenses',
            'is_active' => true,
            'sort_order' => 5,
        ]);

        $furnitureCategory = Category::create([
            'name' => 'Furniture & Fixtures',
            'description' => 'Tables, chairs, and workspace furniture',
            'slug' => 'furniture-fixtures',
            'is_active' => true,
            'sort_order' => 6,
        ]);

        // Create suppliers
        $techSupplier = Supplier::create([
            'name' => 'Tech Solutions Inc.',
            'contact_person' => 'John Smith',
            'email' => '<EMAIL>',
            'phone' => '******-0123',
            'city' => 'Manila',
            'country' => 'Philippines',
            'is_active' => true,
        ]);

        $networkSupplier = Supplier::create([
            'name' => 'Network Pro Supplies',
            'contact_person' => 'Maria Garcia',
            'email' => '<EMAIL>',
            'phone' => '******-0456',
            'city' => 'Quezon City',
            'country' => 'Philippines',
            'is_active' => true,
        ]);

        $toolsSupplier = Supplier::create([
            'name' => 'Professional Tools Corp',
            'contact_person' => 'Robert Chen',
            'email' => '<EMAIL>',
            'phone' => '******-0789',
            'city' => 'Makati',
            'country' => 'Philippines',
            'is_active' => true,
        ]);

        // Networking Equipment
        $this->createNetworkingProducts($networkingCategory, $networkSupplier);

        // Computer Systems
        $this->createComputerProducts($computersCategory, $techSupplier);

        // Tools & Instruments
        $this->createToolsProducts($toolsCategory, $toolsSupplier);

        // Materials & Components
        $this->createMaterialsProducts($materialsCategory, $networkSupplier);

        // Software & Licenses
        $this->createSoftwareProducts($softwareCategory, $techSupplier);

        // Furniture & Fixtures
        $this->createFurnitureProducts($furnitureCategory, $techSupplier);
    }

    private function createNetworkingProducts($category, $supplier)
    {
        $networkingItems = [
            ['name' => 'Patch Panel (48-ports)', 'qty' => 20, 'unit' => 'pcs', 'price' => 2500.00],
            ['name' => 'Patch Panel (24-ports) CAT 6', 'qty' => 20, 'unit' => 'pcs', 'price' => 1800.00],
            ['name' => 'Hub/Switch (24 ports 100mbps)', 'qty' => 10, 'unit' => 'pcs', 'price' => 3500.00],
            ['name' => 'Managed Switch', 'qty' => 2, 'unit' => 'pcs', 'price' => 15000.00],
            ['name' => 'KVM Switch', 'qty' => 1, 'unit' => 'pcs', 'price' => 8000.00],
            ['name' => 'Network Controller', 'qty' => 1, 'unit' => 'pcs', 'price' => 5000.00],
            ['name' => 'Router with Wi-Fi (2.4-5Ghz) Dual Band', 'qty' => 10, 'unit' => 'pcs', 'price' => 4500.00],
            ['name' => 'Wireless Access Point (2.4-5Ghz) Dual Band', 'qty' => 10, 'unit' => 'pcs', 'price' => 3500.00],
            ['name' => 'Network Attached Storage (NAS) HD', 'qty' => 1, 'unit' => 'pcs', 'price' => 25000.00],
            ['name' => 'Internet Subscription (at least 2 MBPS)', 'qty' => 1, 'unit' => 'subscription', 'price' => 2500.00],
        ];

        foreach ($networkingItems as $item) {
            Product::create([
                'name' => $item['name'],
                'sku' => 'NET-' . strtoupper(substr(str_replace([' ', '(', ')', '-'], '', $item['name']), 0, 6)) . '-' . rand(100, 999),
                'description' => 'Computer Systems Servicing NC II - ' . $item['name'],
                'category_id' => $category->id,
                'supplier_id' => $supplier->id,
                'price' => $item['price'],
                'cost' => $item['price'] * 0.7,
                'stock_quantity' => $item['qty'],
                'min_stock_level' => max(1, intval($item['qty'] * 0.2)),
                'max_stock_level' => $item['qty'] * 2,
                'unit' => $item['unit'],
                'track_stock' => true,
                'is_active' => true,
            ]);
        }
    }

    private function createComputerProducts($category, $supplier)
    {
        $computerItems = [
            ['name' => 'Desktop Computer (A6/A10 AMD, i3 4th Gen, 4GB DDR3/DDR4, 500GB HDD)', 'qty' => 10, 'unit' => 'units', 'price' => 25000.00],
            ['name' => 'Laptop Computer (Celeron 4th Gen and above, 4GB, 500 GB HDD)', 'qty' => 10, 'unit' => 'units', 'price' => 30000.00],
            ['name' => 'Server Computer (i5, 9th Gen, 4 GB, 500 GB HDD)', 'qty' => 10, 'unit' => 'units', 'price' => 45000.00],
            ['name' => 'Server Cabinet/Frame/Rack', 'qty' => 10, 'unit' => 'units', 'price' => 15000.00],
            ['name' => 'Uninterruptible Power Supply (UPS) (650 VA)', 'qty' => 10, 'unit' => 'units', 'price' => 3500.00],
            ['name' => 'PC Video Camera/Web Camera', 'qty' => 10, 'unit' => 'units', 'price' => 1500.00],
            ['name' => 'External Optical Drive', 'qty' => 2, 'unit' => 'units', 'price' => 2500.00],
            ['name' => 'USB External HDD, 3.5 enclosure', 'qty' => 1, 'unit' => 'units', 'price' => 4500.00],
            ['name' => 'USB External HDD, 2.5 enclosure', 'qty' => 1, 'unit' => 'units', 'price' => 3500.00],
            ['name' => '3-in-1 Printer with Wireless Access', 'qty' => 1, 'unit' => 'units', 'price' => 8500.00],
        ];

        foreach ($computerItems as $item) {
            Product::create([
                'name' => $item['name'],
                'sku' => 'COMP-' . strtoupper(substr(str_replace([' ', '(', ')', '-', ','], '', $item['name']), 0, 6)) . '-' . rand(100, 999),
                'description' => 'Computer Systems Servicing NC II - ' . $item['name'],
                'category_id' => $category->id,
                'supplier_id' => $supplier->id,
                'price' => $item['price'],
                'cost' => $item['price'] * 0.7,
                'stock_quantity' => $item['qty'],
                'min_stock_level' => max(1, intval($item['qty'] * 0.2)),
                'max_stock_level' => $item['qty'] * 2,
                'unit' => $item['unit'],
                'track_stock' => true,
                'is_active' => true,
            ]);
        }
    }

    private function createToolsProducts($category, $supplier)
    {
        $toolsItems = [
            ['name' => 'Screwdriver (Standard)', 'qty' => 10, 'unit' => 'pcs', 'price' => 150.00],
            ['name' => 'Screwdriver (Philips)', 'qty' => 10, 'unit' => 'pcs', 'price' => 150.00],
            ['name' => 'Precision Screwdrivers Set', 'qty' => 10, 'unit' => 'sets', 'price' => 500.00],
            ['name' => 'Long Nose Pliers', 'qty' => 10, 'unit' => 'pcs', 'price' => 300.00],
            ['name' => 'Combinational Pliers', 'qty' => 10, 'unit' => 'pcs', 'price' => 350.00],
            ['name' => 'Allen Wrench Set', 'qty' => 10, 'unit' => 'sets', 'price' => 400.00],
            ['name' => 'Pass Through Crimping Tool', 'qty' => 10, 'unit' => 'pcs', 'price' => 800.00],
            ['name' => 'Soldering Iron', 'qty' => 10, 'unit' => 'pcs', 'price' => 600.00],
            ['name' => 'Solder Sucker', 'qty' => 10, 'unit' => 'pcs', 'price' => 200.00],
            ['name' => 'Wire Stripper', 'qty' => 5, 'unit' => 'pcs', 'price' => 450.00],
            ['name' => 'Portable Electric Screwdriver', 'qty' => 1, 'unit' => 'units', 'price' => 2500.00],
            ['name' => 'Screw Bit Set', 'qty' => 1, 'unit' => 'sets', 'price' => 800.00],
            ['name' => 'Anti-static Wrist Strap, Elastic Nylon Fiber', 'qty' => 10, 'unit' => 'pcs', 'price' => 250.00],
            ['name' => 'Flash Light/Head Mounted Light', 'qty' => 10, 'unit' => 'pcs', 'price' => 400.00],
            ['name' => 'Can of Compressed Air', 'qty' => 10, 'unit' => 'cans', 'price' => 300.00],
            ['name' => 'Small Portable Vacuum', 'qty' => 1, 'unit' => 'units', 'price' => 3500.00],
            ['name' => 'Krone Punchdown Tool', 'qty' => 10, 'unit' => 'pcs', 'price' => 1200.00],
            ['name' => 'Hot Air Soldering Station', 'qty' => 1, 'unit' => 'units', 'price' => 8500.00],
            ['name' => 'Cleaning Brush', 'qty' => 1, 'unit' => 'units', 'price' => 150.00],
            ['name' => 'Steel Tape, 5 meters', 'qty' => 5, 'unit' => 'pcs', 'price' => 300.00],
            ['name' => 'VOM (Analog/Digital)', 'qty' => 10, 'unit' => 'pcs', 'price' => 1500.00],
            ['name' => 'Anti-static Mat (30 x 50mm)', 'qty' => 10, 'unit' => 'pcs', 'price' => 800.00],
            ['name' => 'Multitester VOM', 'qty' => 10, 'unit' => 'pcs', 'price' => 1200.00],
            ['name' => 'LAN Cable Tester', 'qty' => 10, 'unit' => 'pcs', 'price' => 2500.00],
            ['name' => 'Flash Drive (3.0 8GB)', 'qty' => 10, 'unit' => 'pcs', 'price' => 500.00],
        ];

        foreach ($toolsItems as $item) {
            Product::create([
                'name' => $item['name'],
                'sku' => 'TOOL-' . strtoupper(substr(str_replace([' ', '(', ')', '-', ','], '', $item['name']), 0, 6)) . '-' . rand(100, 999),
                'description' => 'Computer Systems Servicing NC II - ' . $item['name'],
                'category_id' => $category->id,
                'supplier_id' => $supplier->id,
                'price' => $item['price'],
                'cost' => $item['price'] * 0.7,
                'stock_quantity' => $item['qty'],
                'min_stock_level' => max(1, intval($item['qty'] * 0.2)),
                'max_stock_level' => $item['qty'] * 2,
                'unit' => $item['unit'],
                'track_stock' => true,
                'is_active' => true,
            ]);
        }
    }

    private function createMaterialsProducts($category, $supplier)
    {
        $materialsItems = [
            ['name' => 'RJ 45 Pass Through', 'qty' => 400, 'unit' => 'pcs', 'price' => 15.00],
            ['name' => 'Modular Box (RJ45)', 'qty' => 25, 'unit' => 'pcs', 'price' => 80.00],
            ['name' => 'Raceway or Slotted PVC 1.5" x 1.5" x 48"', 'qty' => 20, 'unit' => 'pcs', 'price' => 250.00],
            ['name' => 'UTP Cable (CAT 6)', 'qty' => 1, 'unit' => 'box', 'price' => 8500.00],
            ['name' => 'Contact Cleaner', 'qty' => 1, 'unit' => 'can', 'price' => 350.00],
            ['name' => 'Filler (Lead-free)', 'qty' => 1, 'unit' => 'spool', 'price' => 450.00],
            ['name' => 'Fiber Optic Cable 2 meters (terminated)', 'qty' => 4, 'unit' => 'pcs', 'price' => 1200.00],
            ['name' => 'Battery (9V for VOM)', 'qty' => 20, 'unit' => 'pcs', 'price' => 80.00],
            ['name' => 'Battery (1.5V for VOM)', 'qty' => 10, 'unit' => 'pcs', 'price' => 25.00],
            ['name' => 'Battery (9V for LAN Tester)', 'qty' => 10, 'unit' => 'pcs', 'price' => 80.00],
            ['name' => 'Alcohol (Isopropyl 70%)', 'qty' => 1, 'unit' => 'liter', 'price' => 150.00],
            ['name' => 'Used LAN/NIC for PCI slot', 'qty' => 5, 'unit' => 'pcs', 'price' => 300.00],
            ['name' => 'Good LAN/NIC for PCI slot for replacement', 'qty' => 5, 'unit' => 'pcs', 'price' => 800.00],
            ['name' => 'Used Motherboard', 'qty' => 5, 'unit' => 'pcs', 'price' => 2500.00],
            ['name' => 'Good Motherboard for replacement', 'qty' => 5, 'unit' => 'pcs', 'price' => 6500.00],
            ['name' => 'Used Power Supply', 'qty' => 5, 'unit' => 'pcs', 'price' => 800.00],
            ['name' => 'Good Power Supply for replacement', 'qty' => 5, 'unit' => 'pcs', 'price' => 2500.00],
            ['name' => 'Used AC Cable', 'qty' => 5, 'unit' => 'pcs', 'price' => 150.00],
            ['name' => 'Good AC Cable for replacement', 'qty' => 5, 'unit' => 'pcs', 'price' => 300.00],
            ['name' => 'Used CAT6 Cable 2 meters', 'qty' => 10, 'unit' => 'meters', 'price' => 50.00],
            ['name' => 'Good CAT6 Cable 2 meters for replacement', 'qty' => 10, 'unit' => 'meters', 'price' => 120.00],
        ];

        foreach ($materialsItems as $item) {
            Product::create([
                'name' => $item['name'],
                'sku' => 'MAT-' . strtoupper(substr(str_replace([' ', '(', ')', '-', ','], '', $item['name']), 0, 6)) . '-' . rand(100, 999),
                'description' => 'Computer Systems Servicing NC II - ' . $item['name'],
                'category_id' => $category->id,
                'supplier_id' => $supplier->id,
                'price' => $item['price'],
                'cost' => $item['price'] * 0.7,
                'stock_quantity' => $item['qty'],
                'min_stock_level' => max(1, intval($item['qty'] * 0.2)),
                'max_stock_level' => $item['qty'] * 2,
                'unit' => $item['unit'],
                'track_stock' => true,
                'is_active' => true,
            ]);
        }
    }

    private function createSoftwareProducts($category, $supplier)
    {
        $softwareItems = [
            ['name' => 'Desktop OS (Windows 10 and up - volume license)', 'qty' => 10, 'unit' => 'licenses', 'price' => 8500.00],
            ['name' => 'Network OS (Windows Server 2019 and up)', 'qty' => 1, 'unit' => 'license', 'price' => 45000.00],
            ['name' => 'Office Productivity Software (OpenOffice/LibreOffice/WPS)', 'qty' => 10, 'unit' => 'licenses', 'price' => 0.00],
            ['name' => 'Anti-virus Software (FREE version acceptable)', 'qty' => 10, 'unit' => 'licenses', 'price' => 0.00],
            ['name' => 'Disk Utility Software (FREE Version acceptable)', 'qty' => 10, 'unit' => 'licenses', 'price' => 0.00],
            ['name' => 'Virtualization Software (FREE Version acceptable)', 'qty' => 10, 'unit' => 'licenses', 'price' => 0.00],
            ['name' => 'Disk Creator Software (Bootable USB Image Creator)', 'qty' => 10, 'unit' => 'licenses', 'price' => 0.00],
            ['name' => 'Disk Image Software (Image for Recovery system)', 'qty' => 10, 'unit' => 'licenses', 'price' => 0.00],
            ['name' => 'Application Software (PDF reader, Media player, etc.)', 'qty' => 1, 'unit' => 'lot', 'price' => 0.00],
            ['name' => 'Motherboard Manual and Installer', 'qty' => 26, 'unit' => 'pieces', 'price' => 0.00],
            ['name' => 'Device Driver Installer', 'qty' => 10, 'unit' => 'pieces', 'price' => 0.00],
            ['name' => 'Assorted Computer Books/E-books, Online Magazines/Journals', 'qty' => 1, 'unit' => 'lot', 'price' => 5000.00],
        ];

        foreach ($softwareItems as $item) {
            Product::create([
                'name' => $item['name'],
                'sku' => 'SOFT-' . strtoupper(substr(str_replace([' ', '(', ')', '-', ','], '', $item['name']), 0, 6)) . '-' . rand(100, 999),
                'description' => 'Computer Systems Servicing NC II - ' . $item['name'],
                'category_id' => $category->id,
                'supplier_id' => $supplier->id,
                'price' => $item['price'],
                'cost' => $item['price'] * 0.7,
                'stock_quantity' => $item['qty'],
                'min_stock_level' => max(1, intval($item['qty'] * 0.2)),
                'max_stock_level' => $item['qty'] * 2,
                'unit' => $item['unit'],
                'track_stock' => true,
                'is_active' => true,
            ]);
        }
    }

    private function createFurnitureProducts($category, $supplier)
    {
        $furnitureItems = [
            ['name' => 'Working Table with Chair (for two computers)', 'qty' => 10, 'unit' => 'sets', 'price' => 8500.00],
            ['name' => 'Whiteboard', 'qty' => 1, 'unit' => 'pc', 'price' => 3500.00],
            ['name' => 'Fastener', 'qty' => 1, 'unit' => 'lot', 'price' => 1500.00],
        ];

        foreach ($furnitureItems as $item) {
            Product::create([
                'name' => $item['name'],
                'sku' => 'FURN-' . strtoupper(substr(str_replace([' ', '(', ')', '-', ','], '', $item['name']), 0, 6)) . '-' . rand(100, 999),
                'description' => 'Computer Systems Servicing NC II - ' . $item['name'],
                'category_id' => $category->id,
                'supplier_id' => $supplier->id,
                'price' => $item['price'],
                'cost' => $item['price'] * 0.7,
                'stock_quantity' => $item['qty'],
                'min_stock_level' => max(1, intval($item['qty'] * 0.2)),
                'max_stock_level' => $item['qty'] * 2,
                'unit' => $item['unit'],
                'track_stock' => true,
                'is_active' => true,
            ]);
        }
    }
}
