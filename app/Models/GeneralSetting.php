<?php

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Cache;

/**
 * Class GeneralSetting
 *
 * @property int $id
 * @property string|null $site_name
 * @property string|null $site_description
 * @property string|null $theme_color
 * @property string|null $support_email
 * @property string|null $support_phone
 * @property string|null $google_analytics_id
 * @property string|null $posthog_html_snippet
 * @property string|null $seo_title
 * @property string|null $seo_keywords
 * @property string|null $seo_metadata
 * @property string|null $email_settings
 * @property string|null $email_from_address
 * @property string|null $email_from_name
 * @property string|null $social_network
 * @property string|null $more_configs
 * @property Carbon|null $created_at
 * @property Carbon|null $updated_at
 * @property string|null $school_starting_date
 * @property string|null $school_ending_date
 * @property string|null $school_portal_url
 * @property bool|null $school_portal_enabled
 * @property bool|null $online_enrollment_enabled
 * @property bool|null $school_portal_maintenance
 * @property string $semester
 * @property string|null $enrollment_courses
 * @property string|null $school_portal_logo
 * @property string|null $school_portal_favicon
 * @property string|null $school_portal_title
 * @property string|null $school_portal_description
 * @property bool $enable_clearance_check
 * @property bool $enable_signatures
 * @property string|null $enable_qr_codes
 * @property string|null $enable_public_transactions
 * @property string $enable_support_page
 * @property string|null $features
 * @property string|null $curriculum_year
 */
class GeneralSetting extends Model
{
    protected $table = 'general_settings';

    protected $casts = [
        'seo_metadata' => 'array',
        'email_settings' => 'array',
        'social_network' => 'array',
        'more_configs' => 'array',
        'school_starting_date' => 'date',
        'school_ending_date' => 'date',
        'school_portal_enabled' => 'boolean',
        'online_enrollment_enabled' => 'boolean',
        'school_portal_maintenance' => 'boolean',
        'semester' => 'integer',
        'enrollment_courses' => 'array',
        'enable_signatures' => 'boolean',
        'enable_public_transactions' => 'boolean',
        'enable_qr_codes' => 'boolean',
        'enable_support_page' => 'boolean',
        'features' => 'array',
        'curriculum_year' => 'string',
        'inventory_module_enabled' => 'boolean',
    ];

    protected static function boot()
    {
        parent::boot();

        static::saved(function ($settings): void {
            self::clearCache();
        });
    }

    public static function clearCache()
    {
        Cache::forget('general_settings');
        Cache::forget('api_general_settings');
    }

    public function getSchoolYear(): string
    {
        return $this->getSchoolYearStarting().
            '-'.
            $this->getSchoolYearEnding();
    }

    public function getSchoolYearStarting(): string
    {
        return $this->school_starting_date?->format('Y') ?? 'N/A';
    }

    public function getSchoolYearEnding(): string
    {
        return $this->school_ending_date?->format('Y') ?? 'N/A';
    }

    public function getSchoolYearString(): string
    {
        return $this->getSchoolYearStarting().
            ' - '.
            $this->getSchoolYearEnding();
    }

    public function getSemester(): string
    {
        return match ($this->semester) {
            1 => '1st Semester',
            2 => '2nd Semester',
            default => '1st Semester',
        };
    }

    protected $fillable = [
        'site_name',
        'site_description',
        'theme_color',
        'support_email',
        'support_phone',
        'google_analytics_id',
        'posthog_html_snippet',
        'seo_title',
        'seo_keywords',
        'seo_metadata',
        'email_settings',
        'email_from_address',
        'email_from_name',
        'social_network',
        'more_configs',
        'school_starting_date',
        'school_ending_date',
        'school_portal_url',
        'school_portal_enabled',
        'online_enrollment_enabled',
        'school_portal_maintenance',
        'semester',
        'enrollment_courses',
        'school_portal_logo',
        'school_portal_favicon',
        'school_portal_title',
        'school_portal_description',
        'enable_clearance_check',
        'enable_signatures',
        'enable_qr_codes',
        'enable_public_transactions',
        'enable_support_page',
        'features',
        'curriculum_year',
        'inventory_module_enabled',
    ];
}
