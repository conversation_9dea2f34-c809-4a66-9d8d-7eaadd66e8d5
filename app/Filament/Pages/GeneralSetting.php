<?php

namespace App\Filament\Pages;

use App\Models\Course;
use App\Models\GeneralSetting as GeneralSettingModel;
use Filament\Forms\Components\Actions\Action as FormAction;
use Filament\Forms\Components\DatePicker;
use Filament\Forms\Components\Fieldset;
use Filament\Forms\Components\FileUpload;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Tabs;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Form;
use Filament\Notifications\Notification;
use Filament\Pages\Page;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;
use Joaopaulolndev\FilamentGeneralSettings\Helpers\EmailDataHelper;
use Wallo\FilamentSelectify\Components\ButtonGroup;
use Wallo\FilamentSelectify\Components\ToggleButton;

class GeneralSetting extends Page
{
    // public static function canAccess(): bool
    // {
    //     return Auth::user()->can('page_GeneralSetting');
    // }

    protected static string $view = 'filament-general-settings::filament.pages.general-settings-page';

    protected static bool $shouldRegisterNavigation = false;

    public function getTitle(): string
    {
        return 'School Settings';
    }

    public ?array $data = [];

    public function mount(): void
    {
        // Ensure you are fetching the first record or handle it according to your application logic.
        $settings = GeneralSettingModel::query()->first();
        // dd($settings);
        if ($settings) {
            $this->form->fill($settings->toArray());
        } else {
            // Handle the case where no settings are found if necessary
        }
    }

    public static function canAccess(): bool
    {
        return auth()->user()->can('page_GeneralSettingsPage');
    }

    public function form(Form $form): Form
    {
        $arrTabs = [];

        if (config('filament-general-settings.show_school_portal_tab')) {
            $arrTabs[] = Tabs\Tab::make('School Portal Tab')
                ->label(__('School Portal'))
                ->icon('fas-school')
                ->schema([
                    TextInput::make('school_portal_url')
                        ->label('School Portal URL')
                        ->url()
                        ->columnSpanFull(),
                    ToggleButton::make('school_portal_enabled')->label(
                        'Enable School Portal'
                    ),
                    ToggleButton::make('online_enrollment_enabled')->label(
                        'Enable Online Enrollment'
                    ),
                    ToggleButton::make('enable_clearance_check')->label(
                        'Enable Clearance Checking'
                    ),

                    ToggleButton::make('enable_support_page')->label(
                        'Enable Support Page'
                    ),
                    FileUpload::make('school_portal_logo')
                        ->label('School Portal Logo')
                        ->image()
                        ->disk('supabase')
                        ->visibility('public'),
                    FileUpload::make('school_portal_favicon')
                        ->label('School Portal Favicon')
                        ->image()
                        ->disk('supabase')
                        ->visibility('public'),
                    TextInput::make('school_portal_title')
                        ->label('School Portal Title')
                        ->columnSpanFull(),
                    TextInput::make('school_portal_description')
                        ->label('School Portal Description')
                        ->columnSpanFull(),
                    Section::make('Features')
                        ->schema([
                            Fieldset::make('student_features')
                                ->label('Student Features')
                                ->columnSpan(1)
                                ->columns(1)
                                ->schema([
                                    ToggleButton::make('features.student_features.enable_tuition_fees_page')
                                        ->label('Enable Tuition & Fees Page'),
                                    ToggleButton::make('features.student_features.enable_cheklist_page')
                                        ->label('Enable Checklist Page'),
                                    ToggleButton::make('features.student_features.enable_schedules_page')
                                        ->label('Enable Schedules Page'),
                                    ToggleButton::make('features.student_features.enable_class_rooms_page')
                                        ->label('Enable Class Rooms Page'),
                                    // ToggleButton::make('features.student_features.enable_grades_page')
                                    //     ->label('Enable Grades Page'),
                                    // ToggleButton::make('features.student_features.enable_attendance_page')
                                    //     ->label('Enable Attendance Page'),
                                ]),
                            Fieldset::make('teacher_features')
                                ->label('Teacher Features')
                                ->columnSpan(1)
                                ->columns(1)
                                ->schema([
                                    ToggleButton::make('features.teacher_features.enable_class_rooms_page')
                                        ->label('Enable Class Rooms Page'),
                                    ToggleButton::make('features.teacher_features.enable_schedules_page')
                                        ->label('Enable Schedules Page'),
                                    ToggleButton::make('features.teacher_features.enable_grades_page')
                                        ->label('Enable Grades Page'),
                                    ToggleButton::make('features.teacher_features.enable_attendance_page')
                                        ->label('Enable Attendance Page'),
                                ]),
                        ])
                        ->columns(2),
                ])
                ->columns(3);
        }

        $arrTabs[] = Tabs\Tab::make('Enrollment Settings Tab')
            ->label(__('Enrollment Settings'))
            ->icon('fas-user-graduate')
            ->schema([
                DatePicker::make('school_starting_date')
                    ->label('School Starting Date')
                    ->displayFormat('Y-m-d'),
                DatePicker::make('school_ending_date')
                    ->label('School Ending Date')
                    ->displayFormat('Y-m-d'),
                ButtonGroup::make('semester')
                    ->label('Semester')
                    ->options([
                        '1' => '1st Semester',
                        '2' => '2nd Semester',
                    ]),
                Select::make('enrollment_courses')
                    ->label(
                        'Select the courses that will be available for enrollment'
                    )
                    ->columnSpanFull()

                    ->multiple()
                    ->options(Course::all()->pluck('code', 'id')),
                ToggleButton::make('enable_signatures')->label(
                    'Enable Signatures'
                ),
                ToggleButton::make('enable_qr_codes')
                    ->label('Enable QR codes')
                    ->hint(
                        'Turning this one makes each transaction have thier own QR code'
                    ),
                ToggleButton::make('enable_public_transactions')
                    ->label('make Transactions Public')
                    ->hint(
                        'this will make all transactions can be viewed by the public'
                    ),
            ])
            ->columns(3);

        // Add Module Settings Tab
        $arrTabs[] = Tabs\Tab::make('Module Settings Tab')
            ->label(__('Module Settings'))
            ->icon('heroicon-o-cog-6-tooth')
            ->schema([
                Section::make('Module Configuration')
                    ->description('Enable or disable specific modules in the system')
                    ->schema([
                        ToggleButton::make('inventory_module_enabled')
                            ->label('Enable Inventory Module')
                            ->hint('Enable the inventory management system with products, categories, suppliers, and stock tracking')
                            ->columnSpanFull(),
                    ])
                    ->columns(1),
            ]);

        return $form
            ->schema([Tabs::make('Tabs')->tabs($arrTabs)])
            ->statePath('data');
    }

    // public function castAcademicYearStart(string $value): \DateTime
    // {
    //     return new \DateTime($value);
    // }

    // public function castAcademicYearEnd(string $value): \DateTime
    // {
    //     return new \DateTime($value);
    // }
    protected function getFormActions(): array
    {
        return [
            FormAction::make('Save')
                ->label(__('filament-general-settings::default.save'))
                ->color('primary')
                ->requiresConfirmation()
                ->submit('Update'),
        ];
    }

    public function update(): void
    {
        $data = $this->form->getState();
        // dd($data);
        // $data = EmailDataHelper::setEmailConfigToDatabase($data);

        GeneralSettingModel::updateOrCreate([], $data);
        Cache::forget('general_settings');

        $this->successNotification(
            __('filament-general-settings::default.settings_saved')
        );
        redirect(request()?->header('Referer'));
    }

    private function successNotification(string $title): void
    {
        Notification::make()->title($title)->success()->send();
    }

    private function errorNotification(string $title, string $body): void
    {
        Log::error('[EMAIL] '.$body);

        Notification::make()->title($title)->danger()->body($body)->send();
    }
}
