<?php

namespace App\Filament\Resources\StudentEnrollmentResource\Pages;

use App\Filament\Resources\StudentEnrollmentResource;
use App\Models\Student;
use App\Models\StudentEnrollment;
use Filament\Actions;
use Filament\Notifications\Notification;
use Filament\Resources\Pages\EditRecord;
use Illuminate\Support\Facades\Auth;

class EditStudentEnrollment extends EditRecord
{
    protected static string $resource = StudentEnrollmentResource::class;

    public function clearStudent($studentId)
    {
        try {
            $student = Student::find($studentId);
            if (! $student) {
                Notification::make()
                    ->title('Error')
                    ->body('Student not found.')
                    ->danger()
                    ->send();

                return;
            }

            $user = Auth::user();
            $clearedBy = $user ? $user->name : 'System';

            // Use the method from the Student model to clear the student
            $result = $student->markClearanceAsCleared($clearedBy, 'Cleared via enrollment form');

            if ($result) {
                Notification::make()
                    ->title('Student Cleared')
                    ->body("Student {$student->full_name} has been successfully cleared.")
                    ->success()
                    ->send();
            } else {
                Notification::make()
                    ->title('Error')
                    ->body('Failed to clear the student. Please try again.')
                    ->danger()
                    ->send();
            }
        } catch (\Exception $e) {
            Notification::make()
                ->title('Error')
                ->body('An error occurred while clearing the student: '.$e->getMessage())
                ->danger()
                ->send();
        }
    }

    protected function getHeaderActions(): array
    {
        return [
            Actions\ViewAction::make(),
            Actions\ActionGroup::make([
                Actions\Action::make('recreate_assessment_pdf')
                    ->label('Recreate Assessment PDF')
                    ->icon('heroicon-o-document-duplicate')
                    ->color('info')
                    ->visible(fn (StudentEnrollment $record) => $record->status === 'Verified By Cashier')
                    ->requiresConfirmation()
                    ->modalHeading('Recreate Assessment PDF')
                    ->modalDescription('This will regenerate the assessment PDF for this student enrollment. This is useful when there are issues with class schedules not showing up correctly in the PDF.')
                    ->action(function (StudentEnrollment $record) {
                        try {
                            // Dispatch the PDF generation job
                            \App\Jobs\GenerateAssessmentPdfJob::dispatch($record);

                            Notification::make()
                                ->title('PDF Recreation Queued')
                                ->body('The assessment PDF recreation has been queued and will be processed shortly.')
                                ->success()
                                ->send();

                            \Illuminate\Support\Facades\Log::info("Assessment PDF recreation queued for enrollment {$record->id}", [
                                'enrollment_id' => $record->id,
                                'student_id' => $record->student_id,
                                'student_name' => $record->student->full_name ?? 'Unknown',
                            ]);

                        } catch (\Exception $e) {
                            Notification::make()
                                ->title('PDF Recreation Failed')
                                ->body('Failed to queue PDF recreation: ' . $e->getMessage())
                                ->danger()
                                ->send();

                            \Illuminate\Support\Facades\Log::error("Failed to queue assessment PDF recreation for enrollment {$record->id}", [
                                'enrollment_id' => $record->id,
                                'error' => $e->getMessage(),
                                'trace' => $e->getTraceAsString(),
                            ]);
                        }
                    }),
                Actions\Action::make('resend_assessment_notification')
                    ->label('Resend Assessment Notification')
                    ->icon('heroicon-o-envelope')
                    ->color('warning')
                    ->visible(fn (StudentEnrollment $record) => $record->status === 'Verified By Cashier' && !empty($record->student->email))
                    ->requiresConfirmation()
                    ->modalHeading('Resend Assessment Notification')
                    ->modalDescription('This will resend the assessment notification email to the student.')
                    ->action(function (StudentEnrollment $record) {
                        try {
                            // Dispatch the notification job
                            \App\Jobs\SendAssessmentNotificationJob::dispatch($record);

                            Notification::make()
                                ->title('Notification Queued')
                                ->body('The assessment notification has been queued and will be sent shortly.')
                                ->success()
                                ->send();

                            \Illuminate\Support\Facades\Log::info("Assessment notification resend queued for enrollment {$record->id}", [
                                'enrollment_id' => $record->id,
                                'student_id' => $record->student_id,
                                'student_email' => $record->student->email,
                            ]);

                        } catch (\Exception $e) {
                            Notification::make()
                                ->title('Notification Failed')
                                ->body('Failed to queue notification: ' . $e->getMessage())
                                ->danger()
                                ->send();

                            \Illuminate\Support\Facades\Log::error("Failed to queue assessment notification for enrollment {$record->id}", [
                                'enrollment_id' => $record->id,
                                'error' => $e->getMessage(),
                            ]);
                        }
                    }),
                Actions\DeleteAction::make(),
                Actions\ForceDeleteAction::make(),
                Actions\RestoreAction::make(),
            ])
            ->label('More Options')
            ->icon('heroicon-m-ellipsis-vertical')
            ->size(\Filament\Support\Enums\ActionSize::Small)
            ->color('gray')
            ->button(),
        ];
    }

    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('view', ['record' => $this->getRecord()]);
    }
}
