<?php

namespace App\Filament\Actions;

use App\Models\GeneralSetting;
use Filament\Forms\Components\DateTimePicker;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\Toggle;
use Filament\Forms\Get;
use Filament\Notifications\Notification;
use Filament\Tables\Actions\Action;
use Illuminate\Support\Facades\Auth;

class ClearanceAction extends Action
{
    protected function setUp(): void
    {
        parent::setUp();

        $this->label('Clearance Status')
            ->icon('heroicon-o-check-circle')
            ->color('success')
            ->modalHeading('Manage Student Clearance')
            ->modalDescription('Update the clearance status for this student in the current semester')
            ->modalSubmitActionLabel('Save Clearance Status')
            ->fillForm(function ($record) {
                $clearance = $record->getCurrentClearanceModel();

                if (! $clearance) {
                    return [
                        'is_cleared' => false,
                        'remarks' => null,
                    ];
                }

                return [
                    'is_cleared' => $clearance->is_cleared,
                    'remarks' => $clearance->remarks,
                    'cleared_at' => $clearance->cleared_at,
                ];
            })
            ->form([
                Toggle::make('is_cleared')
                    ->label('Is Cleared')
                    ->helperText('Set whether this student has cleared their requirements for the current semester')
                    ->required(),

                DateTimePicker::make('cleared_at')
                    ->label('Cleared At')
                    ->visible(fn (Get $get): bool => (bool) $get('is_cleared'))
                    ->default(now())
                    ->displayFormat('F j, Y g:i A')
                    ->seconds(false),

                Textarea::make('remarks')
                    ->label('Remarks')
                    ->placeholder('Enter any notes about this clearance status')
                    ->helperText('Optional notes about the clearance status')
                    ->columnSpan(2),
            ])
            ->action(function (array $data, $record): void {
                $settings = GeneralSetting::first();
                $user = Auth::user();
                $clearedBy = $user ? $user->name : 'System';

                if ($data['is_cleared']) {
                    $success = $record->markClearanceAsCleared(
                        $clearedBy,
                        $data['remarks'] ?? null
                    );

                    if ($success) {
                        Notification::make()
                            ->title('Clearance Approved')
                            ->body('The student has been cleared for the '.$settings->getSemester().' of '.$settings->getSchoolYearString())
                            ->success()
                            ->send();
                    }
                } else {
                    $success = $record->markClearanceAsNotCleared($data['remarks'] ?? null);

                    if ($success) {
                        Notification::make()
                            ->title('Clearance Status Updated')
                            ->body('The student is marked as not cleared for the '.$settings->getSemester().' of '.$settings->getSchoolYearString())
                            ->warning()
                            ->send();
                    }
                }
            });
    }

    public static function make(?string $name = null): static
    {
        return parent::make($name);
    }
}
